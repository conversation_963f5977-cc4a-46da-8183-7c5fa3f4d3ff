<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus';

import type { ProductionFormData } from '#/types/production';

import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { ElMessage } from 'element-plus';

import { allMockProductionData } from '#/data/production-mock';
import {
  ProductionStatus,
  ProductType,
  QualityGrade,
} from '#/types/production';

const router = useRouter();
const route = useRoute();

// 响应式数据
const loading = ref(false);
const formRef = ref<FormInstance>();
const isEdit = ref(false);
const pageTitle = ref('新增生产情况');

// 表单数据
const formData = reactive<ProductionFormData>({
  buildingNumber: 1,
  productionDate: '',
  productType: ProductType.FISH,
  quantity: 0,
  unit: 'kg',
  quality: QualityGrade.A,
  temperature: 25,
  ph: 7,
  oxygenLevel: 8,
  feedAmount: 0,
  mortality: 0,
  remarks: '',
  status: ProductionStatus.NORMAL,
});

// 产品类型选项
const productTypeOptions = [
  { label: '鱼类', value: ProductType.FISH },
  { label: '虾类', value: ProductType.SHRIMP },
  { label: '螃蟹', value: ProductType.CRAB },
  { label: '贝类', value: ProductType.SHELLFISH },
];

// 质量等级选项
const qualityOptions = [
  { label: 'A级', value: QualityGrade.A },
  { label: 'B级', value: QualityGrade.B },
  { label: 'C级', value: QualityGrade.C },
];

// 状态选项
const statusOptions = [
  { label: '正常', value: ProductionStatus.NORMAL },
  { label: '警告', value: ProductionStatus.WARNING },
  { label: '危险', value: ProductionStatus.DANGER },
];

// 表单验证规则
const rules: FormRules = {
  buildingNumber: [
    { required: true, message: '请输入楼栋号', trigger: 'blur' },
    {
      type: 'number',
      min: 1,
      max: 19,
      message: '楼栋号必须在1-19之间',
      trigger: 'blur',
    },
  ],
  productionDate: [
    { required: true, message: '请选择生产日期', trigger: 'change' },
  ],
  productType: [
    { required: true, message: '请选择产品类型', trigger: 'change' },
  ],
  quantity: [
    { required: true, message: '请输入产量', trigger: 'blur' },
    { type: 'number', min: 0, message: '产量不能为负数', trigger: 'blur' },
  ],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  quality: [{ required: true, message: '请选择质量等级', trigger: 'change' }],
  temperature: [
    { required: true, message: '请输入水温', trigger: 'blur' },
    {
      type: 'number',
      min: 0,
      max: 50,
      message: '水温必须在0-50°C之间',
      trigger: 'blur',
    },
  ],
  ph: [
    { required: true, message: '请输入pH值', trigger: 'blur' },
    {
      type: 'number',
      min: 0,
      max: 14,
      message: 'pH值必须在0-14之间',
      trigger: 'blur',
    },
  ],
  oxygenLevel: [
    { required: true, message: '请输入溶氧量', trigger: 'blur' },
    { type: 'number', min: 0, message: '溶氧量不能为负数', trigger: 'blur' },
  ],
  feedAmount: [
    { required: true, message: '请输入投料量', trigger: 'blur' },
    { type: 'number', min: 0, message: '投料量不能为负数', trigger: 'blur' },
  ],
  mortality: [
    { required: true, message: '请输入死亡率', trigger: 'blur' },
    {
      type: 'number',
      min: 0,
      max: 100,
      message: '死亡率必须在0-100%之间',
      trigger: 'blur',
    },
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
};

// 方法
const loadData = () => {
  const id = route.params.id as string;
  if (id) {
    isEdit.value = true;
    pageTitle.value = '编辑生产情况';

    // 从模拟数据中查找记录
    const record = allMockProductionData.find((item) => item.id === id);
    if (record) {
      Object.assign(formData, {
        id: record.id,
        buildingNumber: record.buildingNumber,
        productionDate: record.productionDate,
        productType: record.productType,
        quantity: record.quantity,
        unit: record.unit,
        quality: record.quality,
        temperature: record.temperature,
        ph: record.ph,
        oxygenLevel: record.oxygenLevel,
        feedAmount: record.feedAmount,
        mortality: record.mortality,
        remarks: record.remarks,
        status: record.status,
      });
    } else {
      ElMessage.error('记录不存在');
      router.push('/production/list');
    }
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // 模拟API调用
    setTimeout(() => {
      if (isEdit.value) {
        ElMessage.success('更新成功');
      } else {
        ElMessage.success('创建成功');
      }
      loading.value = false;
      router.push('/production/list');
    }, 1000);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleReset = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
};

const handleCancel = () => {
  router.push('/production/list');
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="p-4">
    <el-card>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">{{ pageTitle }}</h2>
          <el-button @click="handleCancel">返回列表</el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="max-w-4xl"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="楼栋号" prop="buildingNumber">
              <el-input-number
                v-model="formData.buildingNumber"
                :min="1"
                :max="19"
                placeholder="请输入楼栋号"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产日期" prop="productionDate">
              <el-date-picker
                v-model="formData.productionDate"
                type="date"
                placeholder="请选择生产日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品类型" prop="productType">
              <el-select
                v-model="formData.productType"
                placeholder="请选择产品类型"
                class="w-full"
              >
                <el-option
                  v-for="option in productTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质量等级" prop="quality">
              <el-select
                v-model="formData.quality"
                placeholder="请选择质量等级"
                class="w-full"
              >
                <el-option
                  v-for="option in qualityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产量" prop="quantity">
              <el-input-number
                v-model="formData.quantity"
                :min="0"
                :precision="2"
                placeholder="请输入产量"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="formData.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="水温(°C)" prop="temperature">
              <el-input-number
                v-model="formData.temperature"
                :min="0"
                :max="50"
                :precision="1"
                placeholder="请输入水温"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="pH值" prop="ph">
              <el-input-number
                v-model="formData.ph"
                :min="0"
                :max="14"
                :precision="1"
                placeholder="请输入pH值"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="溶氧量(mg/L)" prop="oxygenLevel">
              <el-input-number
                v-model="formData.oxygenLevel"
                :min="0"
                :precision="1"
                placeholder="请输入溶氧量"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投料量(kg)" prop="feedAmount">
              <el-input-number
                v-model="formData.feedAmount"
                :min="0"
                :precision="2"
                placeholder="请输入投料量"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="死亡率(%)" prop="mortality">
              <el-input-number
                v-model="formData.mortality"
                :min="0"
                :max="100"
                :precision="1"
                placeholder="请输入死亡率"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                class="w-full"
              >
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="formData.remarks"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
