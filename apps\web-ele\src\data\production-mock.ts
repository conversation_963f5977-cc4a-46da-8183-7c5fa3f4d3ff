import type { ProductionRecord } from '#/types/production';
import { ProductionStatus, ProductType, QualityGrade } from '#/types/production';

/**
 * 生成1-19栋楼的固定模拟数据
 */
export const mockProductionData: ProductionRecord[] = [
  // 1号楼 - 鱼类养殖
  {
    id: '1',
    buildingNumber: 1,
    productionDate: '2024-07-25',
    productType: ProductType.FISH,
    quantity: 1250,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 26.5,
    ph: 7.2,
    oxygenLevel: 8.5,
    feedAmount: 180,
    mortality: 2.1,
    remarks: '水质良好，鱼类生长正常',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 2号楼 - 虾类养殖
  {
    id: '2',
    buildingNumber: 2,
    productionDate: '2024-07-25',
    productType: ProductType.SHRIMP,
    quantity: 890,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 28.2,
    ph: 7.8,
    oxygenLevel: 9.2,
    feedAmount: 125,
    mortality: 1.8,
    remarks: '虾苗活跃，投料正常',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 3号楼 - 螃蟹养殖
  {
    id: '3',
    buildingNumber: 3,
    productionDate: '2024-07-25',
    productType: ProductType.CRAB,
    quantity: 650,
    unit: 'kg',
    quality: QualityGrade.B,
    temperature: 24.8,
    ph: 7.5,
    oxygenLevel: 7.8,
    feedAmount: 95,
    mortality: 3.2,
    remarks: '部分螃蟹脱壳期，需密切观察',
    status: ProductionStatus.WARNING,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 4号楼 - 贝类养殖
  {
    id: '4',
    buildingNumber: 4,
    productionDate: '2024-07-25',
    productType: ProductType.SHELLFISH,
    quantity: 1100,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 22.5,
    ph: 8.1,
    oxygenLevel: 8.8,
    feedAmount: 75,
    mortality: 1.5,
    remarks: '贝类过滤效果良好，水质清澈',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 5号楼 - 鱼类养殖
  {
    id: '5',
    buildingNumber: 5,
    productionDate: '2024-07-25',
    productType: ProductType.FISH,
    quantity: 1380,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 25.8,
    ph: 7.3,
    oxygenLevel: 8.2,
    feedAmount: 195,
    mortality: 2.5,
    remarks: '鱼类摄食积极，生长良好',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 6号楼 - 虾类养殖
  {
    id: '6',
    buildingNumber: 6,
    productionDate: '2024-07-25',
    productType: ProductType.SHRIMP,
    quantity: 720,
    unit: 'kg',
    quality: QualityGrade.B,
    temperature: 29.1,
    ph: 7.6,
    oxygenLevel: 8.9,
    feedAmount: 110,
    mortality: 4.2,
    remarks: '水温偏高，已调整增氧设备',
    status: ProductionStatus.WARNING,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 7号楼 - 螃蟹养殖
  {
    id: '7',
    buildingNumber: 7,
    productionDate: '2024-07-25',
    productType: ProductType.CRAB,
    quantity: 580,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 23.5,
    ph: 7.4,
    oxygenLevel: 8.1,
    feedAmount: 88,
    mortality: 2.8,
    remarks: '螃蟹活力强，品质优良',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 8号楼 - 贝类养殖
  {
    id: '8',
    buildingNumber: 8,
    productionDate: '2024-07-25',
    productType: ProductType.SHELLFISH,
    quantity: 950,
    unit: 'kg',
    quality: QualityGrade.B,
    temperature: 21.8,
    ph: 8.0,
    oxygenLevel: 7.5,
    feedAmount: 68,
    mortality: 3.1,
    remarks: '水温较低，贝类生长缓慢',
    status: ProductionStatus.WARNING,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 9号楼 - 鱼类养殖
  {
    id: '9',
    buildingNumber: 9,
    productionDate: '2024-07-25',
    productType: ProductType.FISH,
    quantity: 1420,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 26.8,
    ph: 7.1,
    oxygenLevel: 8.7,
    feedAmount: 205,
    mortality: 1.9,
    remarks: '鱼类健康状况良好，产量稳定',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 10号楼 - 虾类养殖
  {
    id: '10',
    buildingNumber: 10,
    productionDate: '2024-07-25',
    productType: ProductType.SHRIMP,
    quantity: 680,
    unit: 'kg',
    quality: QualityGrade.C,
    temperature: 30.5,
    ph: 8.2,
    oxygenLevel: 6.8,
    feedAmount: 98,
    mortality: 6.5,
    remarks: '水温过高，溶氧不足，需紧急处理',
    status: ProductionStatus.DANGER,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  }
];

// 继续添加11-19号楼的数据
export const additionalMockData: ProductionRecord[] = [
  // 11号楼 - 螃蟹养殖
  {
    id: '11',
    buildingNumber: 11,
    productionDate: '2024-07-25',
    productType: ProductType.CRAB,
    quantity: 720,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 24.2,
    ph: 7.6,
    oxygenLevel: 8.3,
    feedAmount: 102,
    mortality: 2.3,
    remarks: '螃蟹规格整齐，市场前景良好',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 12号楼 - 贝类养殖
  {
    id: '12',
    buildingNumber: 12,
    productionDate: '2024-07-25',
    productType: ProductType.SHELLFISH,
    quantity: 1200,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 23.1,
    ph: 8.3,
    oxygenLevel: 9.1,
    feedAmount: 82,
    mortality: 1.2,
    remarks: '贝类肥满度高，品质优秀',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 13号楼 - 鱼类养殖
  {
    id: '13',
    buildingNumber: 13,
    productionDate: '2024-07-25',
    productType: ProductType.FISH,
    quantity: 1150,
    unit: 'kg',
    quality: QualityGrade.B,
    temperature: 27.2,
    ph: 6.9,
    oxygenLevel: 7.8,
    feedAmount: 168,
    mortality: 3.8,
    remarks: 'pH值偏低，已投放调节剂',
    status: ProductionStatus.WARNING,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 14号楼 - 虾类养殖
  {
    id: '14',
    buildingNumber: 14,
    productionDate: '2024-07-25',
    productType: ProductType.SHRIMP,
    quantity: 820,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 28.5,
    ph: 7.7,
    oxygenLevel: 8.6,
    feedAmount: 118,
    mortality: 2.1,
    remarks: '虾类蜕壳正常，生长迅速',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 15号楼 - 螃蟹养殖
  {
    id: '15',
    buildingNumber: 15,
    productionDate: '2024-07-25',
    productType: ProductType.CRAB,
    quantity: 490,
    unit: 'kg',
    quality: QualityGrade.C,
    temperature: 25.8,
    ph: 7.2,
    oxygenLevel: 7.2,
    feedAmount: 75,
    mortality: 5.5,
    remarks: '发现病害迹象，已隔离处理',
    status: ProductionStatus.DANGER,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 16号楼 - 贝类养殖
  {
    id: '16',
    buildingNumber: 16,
    productionDate: '2024-07-25',
    productType: ProductType.SHELLFISH,
    quantity: 1050,
    unit: 'kg',
    quality: QualityGrade.B,
    temperature: 22.8,
    ph: 8.1,
    oxygenLevel: 8.4,
    feedAmount: 72,
    mortality: 2.8,
    remarks: '贝类开口率良好，摄食正常',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 17号楼 - 鱼类养殖
  {
    id: '17',
    buildingNumber: 17,
    productionDate: '2024-07-25',
    productType: ProductType.FISH,
    quantity: 1320,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 26.1,
    ph: 7.4,
    oxygenLevel: 8.9,
    feedAmount: 188,
    mortality: 1.7,
    remarks: '鱼类体色鲜艳，活力充沛',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 18号楼 - 虾类养殖
  {
    id: '18',
    buildingNumber: 18,
    productionDate: '2024-07-25',
    productType: ProductType.SHRIMP,
    quantity: 750,
    unit: 'kg',
    quality: QualityGrade.B,
    temperature: 29.2,
    ph: 7.9,
    oxygenLevel: 8.1,
    feedAmount: 108,
    mortality: 3.5,
    remarks: '虾类密度适中，需注意水质变化',
    status: ProductionStatus.WARNING,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  },
  // 19号楼 - 螃蟹养殖
  {
    id: '19',
    buildingNumber: 19,
    productionDate: '2024-07-25',
    productType: ProductType.CRAB,
    quantity: 680,
    unit: 'kg',
    quality: QualityGrade.A,
    temperature: 24.5,
    ph: 7.5,
    oxygenLevel: 8.2,
    feedAmount: 95,
    mortality: 2.6,
    remarks: '螃蟹膏满黄肥，即将上市',
    status: ProductionStatus.NORMAL,
    createdAt: '2024-07-25T08:00:00Z',
    updatedAt: '2024-07-25T18:00:00Z'
  }
];

// 合并所有模拟数据
export const allMockProductionData = [...mockProductionData, ...additionalMockData];
