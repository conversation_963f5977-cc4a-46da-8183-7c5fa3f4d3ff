import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:factory',
      order: 2,
      title: '生产管理',
    },
    name: 'Production',
    path: '/production',
    children: [
      {
        name: 'ProductionList',
        path: '/list',
        component: () => import('#/views/production/list/index.vue'),
        meta: {
          affixTab: false,
          icon: 'lucide:list',
          title: '生产情况',
        },
      },
      {
        name: 'ProductionAdd',
        path: '/add',
        component: () => import('#/views/production/form/index.vue'),
        meta: {
          hideInMenu: true,
          icon: 'lucide:plus',
          title: '新增生产情况',
        },
      },
      {
        name: 'ProductionEdit',
        path: '/edit/:id',
        component: () => import('#/views/production/form/index.vue'),
        meta: {
          hideInMenu: true,
          icon: 'lucide:edit',
          title: '编辑生产情况',
        },
      },
      {
        name: 'ProductionDetail',
        path: '/detail/:id',
        component: () => import('#/views/production/detail/index.vue'),
        meta: {
          hideInMenu: true,
          icon: 'lucide:eye',
          title: '生产详情',
        },
      },
    ],
  },
];

export default routes;
