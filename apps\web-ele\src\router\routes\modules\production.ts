import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:factory',
      order: 2,
      title: $t('page.production.title'),
    },
    name: 'Production',
    path: '/production',
    children: [
      {
        name: 'ProductionList',
        path: '/list',
        component: () => import('#/views/production/list/index.vue'),
        meta: {
          affixTab: false,
          icon: 'lucide:list',
          title: $t('page.production.list'),
        },
      },
      {
        name: 'ProductionAdd',
        path: '/add',
        component: () => import('#/views/production/form/index.vue'),
        meta: {
          hideInMenu: true,
          icon: 'lucide:plus',
          title: $t('page.production.add'),
        },
      },
      {
        name: 'ProductionEdit',
        path: '/edit/:id',
        component: () => import('#/views/production/form/index.vue'),
        meta: {
          hideInMenu: true,
          icon: 'lucide:edit',
          title: $t('page.production.edit'),
        },
      },
      {
        name: 'ProductionDetail',
        path: '/detail/:id',
        component: () => import('#/views/production/detail/index.vue'),
        meta: {
          hideInMenu: true,
          icon: 'lucide:eye',
          title: $t('page.production.detail'),
        },
      },
    ],
  },
];

export default routes;
