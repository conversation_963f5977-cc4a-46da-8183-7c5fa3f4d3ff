<script setup lang="ts">
import type { ProductionRecord } from '#/types/production';

import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { ArrowLeft } from '@vben/icons';

import { ElMessage } from 'element-plus';

import { allMockProductionData } from '#/data/production-mock';
import { ProductionStatus, ProductType } from '#/types/production';

const router = useRouter();
const route = useRoute();

// 响应式数据
const loading = ref(false);
const record = ref<null | ProductionRecord>(null);

// 方法
const loadData = () => {
  loading.value = true;
  const id = route.params.id as string;

  // 从模拟数据中查找记录
  const foundRecord = allMockProductionData.find((item) => item.id === id);

  setTimeout(() => {
    if (foundRecord) {
      record.value = foundRecord;
    } else {
      ElMessage.error('记录不存在');
      router.push('/production/list');
    }
    loading.value = false;
  }, 300);
};

const handleEdit = () => {
  if (record.value) {
    router.push(`/production/edit/${record.value.id}`);
  }
};

const handleBack = () => {
  router.push('/production/list');
};

const getStatusType = (status: ProductionStatus) => {
  switch (status) {
    case ProductionStatus.DANGER: {
      return 'danger';
    }
    case ProductionStatus.NORMAL: {
      return 'success';
    }
    case ProductionStatus.WARNING: {
      return 'warning';
    }
    default: {
      return 'info';
    }
  }
};

const getStatusText = (status: ProductionStatus) => {
  switch (status) {
    case ProductionStatus.DANGER: {
      return '危险';
    }
    case ProductionStatus.NORMAL: {
      return '正常';
    }
    case ProductionStatus.WARNING: {
      return '警告';
    }
    default: {
      return status;
    }
  }
};

const getProductTypeText = (type: ProductType) => {
  switch (type) {
    case ProductType.CRAB: {
      return '螃蟹';
    }
    case ProductType.FISH: {
      return '鱼类';
    }
    case ProductType.SHELLFISH: {
      return '贝类';
    }
    case ProductType.SHRIMP: {
      return '虾类';
    }
    default: {
      return type;
    }
  }
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="p-4">
    <el-card v-loading="loading">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">生产情况详情</h2>
          <div class="space-x-2">
            <el-button @click="handleBack">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
            <el-button type="primary" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="record" class="max-w-4xl">
        <!-- 基本信息 -->
        <div class="mb-6">
          <h3 class="mb-4 text-base font-medium text-gray-700">基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  楼栋号
                </label>
                <div class="text-lg font-semibold">
                  {{ record.buildingNumber }}号楼
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  生产日期
                </label>
                <div class="text-lg">{{ record.productionDate }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  状态
                </label>
                <el-tag :type="getStatusType(record.status)" size="large">
                  {{ getStatusText(record.status) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 产品信息 -->
        <div class="mb-6">
          <h3 class="mb-4 text-base font-medium text-gray-700">产品信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  产品类型
                </label>
                <div class="text-lg">
                  {{ getProductTypeText(record.productType) }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  产量
                </label>
                <div class="text-lg font-semibold text-blue-600">
                  {{ record.quantity }}{{ record.unit }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  质量等级
                </label>
                <div class="text-lg">
                  <el-tag
                    :type="
                      record.quality === 'A'
                        ? 'success'
                        : record.quality === 'B'
                          ? 'warning'
                          : 'danger'
                    "
                  >
                    {{ record.quality }}级
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 环境参数 -->
        <div class="mb-6">
          <h3 class="mb-4 text-base font-medium text-gray-700">环境参数</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  水温
                </label>
                <div class="text-lg">{{ record.temperature }}°C</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  pH值
                </label>
                <div class="text-lg">{{ record.ph }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  溶氧量
                </label>
                <div class="text-lg">{{ record.oxygenLevel }}mg/L</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 养殖数据 -->
        <div class="mb-6">
          <h3 class="mb-4 text-base font-medium text-gray-700">养殖数据</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  投料量
                </label>
                <div class="text-lg">{{ record.feedAmount }}kg</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  死亡率
                </label>
                <div
                  class="text-lg"
                  :class="
                    record.mortality > 5
                      ? 'text-red-600'
                      : record.mortality > 3
                        ? 'text-yellow-600'
                        : 'text-green-600'
                  "
                >
                  {{ record.mortality }}%
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 备注信息 -->
        <div v-if="record.remarks" class="mb-6">
          <h3 class="mb-4 text-base font-medium text-gray-700">备注信息</h3>
          <div class="rounded-lg bg-gray-50 p-4">
            <p class="text-gray-700">{{ record.remarks }}</p>
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="mb-6">
          <h3 class="mb-4 text-base font-medium text-gray-700">时间信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  创建时间
                </label>
                <div class="text-sm text-gray-500">{{ record.createdAt }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="mb-4">
                <label class="mb-1 block text-sm font-medium text-gray-600">
                  更新时间
                </label>
                <div class="text-sm text-gray-500">{{ record.updatedAt }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 数据分析卡片 -->
        <div class="mt-6 grid grid-cols-1 gap-4 md:grid-cols-3">
          <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div class="text-sm font-medium text-blue-600">产量指标</div>
            <div class="mt-1 text-2xl font-bold text-blue-700">
              {{ record.quantity }}{{ record.unit }}
            </div>
            <div class="mt-1 text-xs text-blue-500">
              质量等级: {{ record.quality }}
            </div>
          </div>

          <div class="rounded-lg border border-green-200 bg-green-50 p-4">
            <div class="text-sm font-medium text-green-600">环境状况</div>
            <div class="mt-1 text-lg font-bold text-green-700">
              {{ record.temperature }}°C / pH{{ record.ph }}
            </div>
            <div class="mt-1 text-xs text-green-500">
              溶氧: {{ record.oxygenLevel }}mg/L
            </div>
          </div>

          <div
            class="rounded-lg border p-4"
            :class="
              record.mortality > 5
                ? 'border-red-200 bg-red-50'
                : record.mortality > 3
                  ? 'border-yellow-200 bg-yellow-50'
                  : 'border-green-200 bg-green-50'
            "
          >
            <div
              class="text-sm font-medium"
              :class="
                record.mortality > 5
                  ? 'text-red-600'
                  : record.mortality > 3
                    ? 'text-yellow-600'
                    : 'text-green-600'
              "
            >
              健康状况
            </div>
            <div
              class="mt-1 text-lg font-bold"
              :class="
                record.mortality > 5
                  ? 'text-red-700'
                  : record.mortality > 3
                    ? 'text-yellow-700'
                    : 'text-green-700'
              "
            >
              死亡率 {{ record.mortality }}%
            </div>
            <div
              class="mt-1 text-xs"
              :class="
                record.mortality > 5
                  ? 'text-red-500'
                  : record.mortality > 3
                    ? 'text-yellow-500'
                    : 'text-green-500'
              "
            >
              投料: {{ record.feedAmount }}kg
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
