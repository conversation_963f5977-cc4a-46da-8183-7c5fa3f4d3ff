<script setup lang="ts">
import type {
  ProductionQueryParams,
  ProductionRecord,
} from '#/types/production';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Delete, Plus } from '@vben/icons';

import { ElMessage, ElMessageBox } from 'element-plus';

import { allMockProductionData } from '#/data/production-mock';
import { ProductionStatus, ProductType } from '#/types/production';

const router = useRouter();

// 响应式数据
const loading = ref(false);
const tableData = ref<ProductionRecord[]>([]);
const total = ref(0);
const selectedRows = ref<ProductionRecord[]>([]);

// 查询参数
const queryParams = reactive<ProductionQueryParams>({
  page: 1,
  pageSize: 10,
  buildingNumber: undefined,
  productType: undefined,
  status: undefined,
  keyword: '',
});

// 状态选项
const statusOptions = [
  { label: '正常', value: ProductionStatus.NORMAL },
  { label: '警告', value: ProductionStatus.WARNING },
  { label: '危险', value: ProductionStatus.DANGER },
];

// 产品类型选项
const productTypeOptions = [
  { label: '鱼类', value: ProductType.FISH },
  { label: '虾类', value: ProductType.SHRIMP },
  { label: '螃蟹', value: ProductType.CRAB },
  { label: '贝类', value: ProductType.SHELLFISH },
];

// 计算属性
const filteredData = computed(() => {
  let data = [...allMockProductionData];

  // 按楼栋号筛选
  if (queryParams.buildingNumber) {
    data = data.filter(
      (item) => item.buildingNumber === queryParams.buildingNumber,
    );
  }

  // 按产品类型筛选
  if (queryParams.productType) {
    data = data.filter((item) => item.productType === queryParams.productType);
  }

  // 按状态筛选
  if (queryParams.status) {
    data = data.filter((item) => item.status === queryParams.status);
  }

  // 按关键词搜索
  if (queryParams.keyword) {
    const keyword = queryParams.keyword.toLowerCase();
    data = data.filter(
      (item) =>
        item.buildingNumber.toString().includes(keyword) ||
        item.productType.toLowerCase().includes(keyword) ||
        item.remarks?.toLowerCase().includes(keyword),
    );
  }

  total.value = data.length;

  // 分页
  const start = (queryParams.page! - 1) * queryParams.pageSize!;
  const end = start + queryParams.pageSize!;

  return data.slice(start, end);
});

// 方法
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = filteredData.value;
    loading.value = false;
  }, 300);
};

const handleSearch = () => {
  queryParams.page = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(queryParams, {
    page: 1,
    pageSize: 10,
    buildingNumber: undefined,
    productType: undefined,
    status: undefined,
    keyword: '',
  });
  loadData();
};

const handleAdd = () => {
  router.push('/production/add');
};

const handleEdit = (row: ProductionRecord) => {
  router.push(`/production/edit/${row.id}`);
};

const handleView = (row: ProductionRecord) => {
  router.push(`/production/detail/${row.id}`);
};

const handleDelete = async (row: ProductionRecord) => {
  try {
    await ElMessageBox.confirm('确定要删除这条生产记录吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    // 这里应该调用删除API
    ElMessage.success('删除成功');
    loadData();
  } catch {
    // 用户取消删除
  }
};

const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    // 这里应该调用批量删除API
    ElMessage.success('批量删除成功');
    selectedRows.value = [];
    loadData();
  } catch {
    // 用户取消删除
  }
};

const handleSelectionChange = (selection: ProductionRecord[]) => {
  selectedRows.value = selection;
};

const handlePageChange = (page: number) => {
  queryParams.page = page;
  loadData();
};

const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  queryParams.page = 1;
  loadData();
};

const getStatusType = (status: ProductionStatus) => {
  switch (status) {
    case ProductionStatus.DANGER: {
      return 'danger';
    }
    case ProductionStatus.NORMAL: {
      return 'success';
    }
    case ProductionStatus.WARNING: {
      return 'warning';
    }
    default: {
      return 'info';
    }
  }
};

const getStatusText = (status: ProductionStatus) => {
  switch (status) {
    case ProductionStatus.DANGER: {
      return '危险';
    }
    case ProductionStatus.NORMAL: {
      return '正常';
    }
    case ProductionStatus.WARNING: {
      return '警告';
    }
    default: {
      return status;
    }
  }
};

const getProductTypeText = (type: ProductType) => {
  switch (type) {
    case ProductType.CRAB: {
      return '螃蟹';
    }
    case ProductType.FISH: {
      return '鱼类';
    }
    case ProductType.SHELLFISH: {
      return '贝类';
    }
    case ProductType.SHRIMP: {
      return '虾类';
    }
    default: {
      return type;
    }
  }
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="p-4">
    <!-- 搜索表单 -->
    <el-card class="mb-4">
      <el-form :model="queryParams" inline>
        <el-form-item label="楼栋号">
          <el-input-number
            v-model="queryParams.buildingNumber"
            :min="1"
            :max="19"
            placeholder="请输入楼栋号"
            clearable
          />
        </el-form-item>
        <el-form-item label="产品类型">
          <el-select
            v-model="queryParams.productType"
            placeholder="请选择产品类型"
            clearable
          >
            <el-option
              v-for="option in productTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入楼栋号或产品类型搜索"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="mb-4">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增生产情况
      </el-button>
      <el-button
        type="danger"
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="buildingNumber" label="楼栋号" width="100" />
        <el-table-column prop="productionDate" label="生产日期" width="120" />
        <el-table-column prop="productType" label="产品类型" width="100">
          <template #default="{ row }">
            {{ getProductTypeText(row.productType) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="产量" width="100">
          <template #default="{ row }">
            {{ row.quantity }}{{ row.unit }}
          </template>
        </el-table-column>
        <el-table-column prop="quality" label="质量等级" width="100" />
        <el-table-column prop="temperature" label="水温" width="100">
          <template #default="{ row }"> {{ row.temperature }}°C </template>
        </el-table-column>
        <el-table-column prop="mortality" label="死亡率" width="100">
          <template #default="{ row }"> {{ row.mortality }}% </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>
